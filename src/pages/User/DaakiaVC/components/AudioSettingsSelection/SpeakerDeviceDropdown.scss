// SCSS Variables - Colors from ControlBar.scss and VideoConference.scss
$primary-bg-dark: #000;
$secondary-bg-dark: #1f1f1f;
$tertiary-bg-dark: #2b2b2b;
$quaternary-bg-dark: #2d2d38;
$border-dark: #111;
$border-secondary: #242424;
$accent-blue: #0a84ff;
$accent-blue-hover: #40a9ff;
$white: #fff;
$text-primary: #ffffff;
$text-secondary: #8c8c8c;
$hover-overlay: rgba(255, 255, 255, 0.1);
$shadow-dark: rgba(0, 0, 0, 0.75);

.speaker-device-dropdown {
  position: relative;
  display: inline-block;

  &-button {
    background: $secondary-bg-dark;
    border: 1px solid $border-secondary;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    width: 180px;
    max-width: 180px;
    min-width: 180px;
    transition: all 0.2s ease;
    color: $text-primary;

    &:hover {
      background-color: $hover-overlay;
      border-color: $accent-blue-hover;
    }

    &:focus {
      outline: none;
      border-color: $accent-blue;
      background-color: $hover-overlay;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .device-icon {
      width: 16px;
      height: 16px;
      flex-shrink: 0;
    }

    .device-name {
      flex: 1;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
    }

    .dropdown-arrow {
      color: $text-secondary;
      font-size: 12px;
      flex-shrink: 0;
    }
  }

  &-menu {
    background: $secondary-bg-dark;
    border: 1px solid $border-secondary;
    border-radius: 4px;
    box-shadow: 0px 0px 5px 0px $shadow-dark;
    width: 180px;
    max-width: 180px;
    min-width: 180px;
    max-height: 200px;
    overflow-y: auto;
    padding: 4px;
    margin-bottom: 8px;

    .device-option {
      width: 100%;
      text-align: left;
      padding: 8px 12px;
      border: none;
      background: transparent;
      cursor: pointer;
      border-radius: 4px;
      margin-bottom: 2px;
      color: $text-primary;
      transition: all 0.2s ease;
      font-size: 14px;
      display: block;

      &:hover {
        background-color: $hover-overlay;
      }

      &.selected {
        background-color: $accent-blue !important;
        color: $white !important;
        font-weight: 500;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
