import React from "react";
import "../HomeElectron/HomeElectron.scss";
import isElectron from "is-electron";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import {
  removeUserSubscription,
  selectProfileData,
  updateLanguageList,
  updateProfile,
  updateUserAccountData,
} from "../../../../redux/UserSlice/index.slice";

import { logout } from "../../../../redux/AuthSlice/index.slice";

import { UserAuthServices } from "../../../../services";
import { logger, redirectToDesktopAppForLogin } from "../../../../utils";
import userRoutesMap from "../../../../routeControl/userRoutes";
import { ReactComponent as Logo } from "../HomeElectron/Assets/DaakiaLogo.svg";

export default function ElectronLogin() {
  const userData = useSelector(selectProfileData);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const accountLogout = async () => {
    try {
      await UserAuthServices.logoutService();
      dispatch(logout(navigate, userData?.userRole));
      dispatch(updateProfile({}));
      dispatch(removeUserSubscription());
      dispatch(updateLanguageList([]));
      dispatch(updateUserAccountData([]));
      window.location.href = userRoutesMap.LOGIN.path;
      // setLocalStorage("isLoggedOut", true);
    } catch (error) {
      logger(error);
    }
  };

  const handleRedirectToApp = () => {
    if (!isElectron() && userData?.username) {

      redirectToDesktopAppForLogin(userData?.username);

      // Attempt to close the tab after a small delay
      setTimeout(() => {
        window.close();
      }, 5000); // Allow time for the redirection to initiate
    }
  };

  const userContact =
    userData?.UserProfile?.email || userData?.UserProfile?.mobile_no;

  return (
    <div className="home-electron">
      <Logo className="home-electron-logo" />
      <h2 className="home-elctron-head">Log In As</h2>
      <h2 className="home-elctron-head">{userContact}</h2>
      <div className="home-electron-login">
        <a
          className="home-electron-login-button"
          onClick={() => {
            handleRedirectToApp();
          }}
        >
          Open the desktop app
        </a>
        <p>
          Couldn&apos;t recognize {userContact} |{" "}
          <Link
            to={userRoutesMap.LOGIN.path}
            onClick={(e) => {
              e.preventDefault();
              accountLogout();
            }}
          >
            Log in with another account
          </Link>
        </p>
      </div>
    </div>
  );
}
