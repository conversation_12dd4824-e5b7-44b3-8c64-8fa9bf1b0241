import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React, { useEffect, useState } from "react";
import { VideoConferenceService } from "../../../../services";
import { baseUrlGenerator, encoder, modalNotification } from "../../../../utils";
import { ReactComponent as CopyLink } from "../Assets/CopyLink.svg";
import { ReactComponent as CopyLinkIcon } from "../Assets/CopyLinkIcon.svg";
import userRoutesMap from "../../../../routeControl/userRoutes";
import "./PersonalRoomModal.scss";

export default function PersonalRoomModal({
  startMeetingModal,
  setStartMeetingModal,
}) {
  const [eventName, setEventName] = useState("");
  const [eventMode, setEventMode] = useState("follow");
  const [editPersonalMeeting, setEditPersonalMeeting] = useState(false);
  const [personalMeetingRoomId, setPersonalMeetingRoomId] = useState("");
  const [personalMeetingRoomUid, setPersonalMeetingRoomUid] = useState("");
  const [originalEventName, setOriginalEventName] = useState("");
  const [originalEventMode, setOriginalEventMode] = useState("");

  const getPersonalMeetingRoomDetails = async () => {
    const res = await VideoConferenceService.getPersonalMeetingRoomIdService();
    const { success, message, data } = res;
    if (success === 1) {

      setPersonalMeetingRoomId(data?.id);
      setPersonalMeetingRoomUid(data?.room_uid);
      setEventName(data?.event_name);
      setEventMode(data?.event_mode);
    } else {
      modalNotification({
        type: "error",
        message,
      });
    }
  };

  const updatePersonalMeetingRoomDetails = async () => {
    const res = await VideoConferenceService.updatePlanMeetingsService(personalMeetingRoomId, {
      event_name: eventName,
      event_mode: eventMode,
    });
    const { success, message } = res;
    if (success === 1) {
      modalNotification({
        type: "success",
        message,
      });
      getPersonalMeetingRoomDetails();
      setEditPersonalMeeting(false);
    } else {
      modalNotification({
        type: "error",
        message,
      });
    }
  };

  const handleRoomNameChange = (e) => {
    setEventName(e.target.value);
  };

  useEffect(() => {
    getPersonalMeetingRoomDetails();
  }, []);


  return (
    <Modal
      open={startMeetingModal}
      onCancel={() => setStartMeetingModal(false)}
      width="80%"
      footer={null}
      className="personal-meeting-modal"
    >
      <div className="personal-meeting-modal-content">
        <h2 className="personal-meeting-modal-content-title">
          Your Personal Room Link
        </h2>
        <p className="personal-meeting-modal-content-subtitle">
          &quot;Start or share this quick access room anytime.&quot;
        </p>

        <div className="personal-meeting-modal-content-input">
          <div className="personal-meeting-modal-content-input-group">
            <fieldset className="custom-fieldset">
              <legend className="custom-legend">
                Room Name<span className="required">*</span>
              </legend>
              <Tooltip title="Edit your Personal Room Name." placement="top">
              <input
                type="text"
                className="custom-input"
                value={eventName}
                onChange={handleRoomNameChange}
                required
                placeholder={eventName}
                readOnly={!editPersonalMeeting}
                style={{
                  accentColor: !editPersonalMeeting ? "#384048" : undefined,
                  // If you want to match the border color as well, you can add:
                  // borderColor: !editPersonalMeeting ? "#3B60E4" : undefined,
                }}
              />
              </Tooltip>
            </fieldset>
          </div>
          <div className="personal-meeting-modal-content-input-group">
            <fieldset className="custom-fieldset">
              <legend className="custom-legend">
                Invite Link<span className="required">*</span>
              </legend>
              <div className="invite-link-content">
                <CopyLinkIcon style={{ marginRight: "8px" }} />
                {(() => {
                  const fullUrl = `${baseUrlGenerator(
                    userRoutesMap.DAAKIA_VC_MEET.path
                  )}/${encoder(personalMeetingRoomUid)}`;
                  // Show the first (fullUrl.length - 15) chars, then "..." at the end if long
                  const displayUrl =
                    fullUrl.length > 18
                      ? `${fullUrl.slice(0, fullUrl.length - 13)}...`
                      : fullUrl;
                  return (
                    <a
                      href={fullUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="personal-meeting-modal-link"
                    >
                      {displayUrl}
                    </a>
                  );
                })()}
                <span
                  className="personal-meeting-modal-copy"
                  onClick={() => {
                    const url = `${baseUrlGenerator(
                      userRoutesMap.DAAKIA_VC_MEET.path
                    )}/${encoder(personalMeetingRoomUid)}`;
                    window.navigator.clipboard.writeText(url);
                    modalNotification({
                      type: "success",
                      message: "Link copied to clipboard",
                    });
                  }}
                  title="Copy link"
                >
                  <CopyLink style={{ marginLeft: "8px", cursor: "pointer" }} />
                </span>
              </div>
            </fieldset>
          </div>
        </div>
        <div className="personal-meeting-modal-content-section">
          <label className="section-label">Meeting Mode</label>
          <div className="radio-group">
          <Tooltip 
              title={<div style={{ textAlign: "center" }}>Participants will automatically enter the meeting once started by host.</div>} 
              placement="top"
            >
            <label className="radio-label">
              <input
                type="radio"
                name="security"
                value="follow"
                style={{ accentColor: "#3B60E4" }}
                checked={eventMode === "follow"}
                onChange={() => editPersonalMeeting && setEventMode("follow")}
                disabled={!editPersonalMeeting}
              />
              Follow Mode
            </label> 
            </Tooltip>
            <Tooltip title={<div style={{ textAlign: "center" }}>Participants can start the meeting without having to wait for the host.</div>}
            placement="top"
            >
            <label className="radio-label">
              <input
                type="radio"
                name="security"
                value="participant"
                style={{ accentColor: "#3B60E4" }}
                checked={eventMode === "participant"}
                onChange={() =>
                  editPersonalMeeting && setEventMode("participant")
                }
                disabled={!editPersonalMeeting}
              />
              Participant Mode
            </label>
            </Tooltip>
            <Tooltip 
              title={<div style={{ textAlign: "center" }}>Participants will be waiting in the lobby until host admits them.</div>} 
              placement="top"
            >
            <label className="radio-label">
              <input
                type="radio"
                name="security"
                value="lobby"
                style={{ accentColor: "#3B60E4" }}
                checked={eventMode === "lobby"}
                defaultChecked={eventMode === "lobby"}
                onChange={() => editPersonalMeeting && setEventMode("lobby")}
                disabled={!editPersonalMeeting}
              />
              Lobby Mode
            </label>
            </Tooltip>
          </div>
        </div>

        {/* <div className="personal-meeting-modal-content-section">
            <label className="section-label">Audio</label>
            <div className="radio-group">
              <label className="radio-label">
                <input type="radio" name="audio" defaultChecked style={{ accentColor: "#3B60E4" }} />
                Computer Audio
              </label>
            </div>
          </div>
          <div className="personal-meeting-modal-content-section video-section">
            <div className="video-section-column">
              <div
                className="video-section-row"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "32px", // Ensures same gap between Video and Host as Host and radio
                }}
              >
                <label className="section-label" style={{ margin: 0 }}>
                  Video
                </label>
                <span className="video-role" style={{ margin: 0 }}>
                  Host
                </span>
                <label className="radio-label">
                  <input
                    type="radio"
                    name="host-video"
                    defaultChecked
                    style={{ accentColor: "#3B60E4" }}
                  />
                  <span className="video-toggle">On</span>
                </label>
              </div>
              <div
                className="video-section-row participant-row"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "32px", // Ensures same gap between Participant and radio
                  marginTop: "24px", // Optional: vertical spacing between rows
                }}
              >
                <span className="video-role" style={{ margin: 0 }}>
                  Participant
                </span>
                <label className="radio-label">
                  <input
                    type="radio"
                    name="participant-video"
                    style={{ accentColor: "#3B60E4" }}
                  />
                  <span className="video-toggle">Off</span>
                </label>
              </div>
            </div>
          </div> */}

        <div className="personal-meeting-modal-content-actions">
          <button
            className="action-btn start"
            disabled={editPersonalMeeting}
            onClick={() => {
              const url = `${baseUrlGenerator(
                userRoutesMap.DAAKIA_VC_MEET.path
              )}/${encoder(personalMeetingRoomUid)}`;
              window.open(url, "_blank", "noopener,noreferrer");
            }}
          >
            Start Meeting
          </button>
          {editPersonalMeeting ? (
            <>
              <button
                className="action-btn save"
                onClick={updatePersonalMeetingRoomDetails}
              >
                Save
              </button>
              <button
                className="action-btn discard"
                
                onClick={() => {
                  setEventName(originalEventName);
                  setEventMode(originalEventMode);
                  setEditPersonalMeeting(false);
                }}
              >
                Discard Changes
              </button>
            </>
          ) : (
            <button
              className="action-btn edit"
              onClick={() => {
                setOriginalEventName(eventName);
                setOriginalEventMode(eventMode);
                setEditPersonalMeeting(true);
              }}
            >
              Edit
            </button>
          )}
        </div>
      </div>
      {/* Redesigned Personal Meeting URL Modal */}
      {/* <div className="personal-meeting-modal-content">
          <h1 className="personal-meeting-modal-title">{t("text.videoConferencing.personalMeetingUrl")}</h1>
          <div className="personal-meeting-modal-link-box">
            <a
              href={`${baseUrlGenerator(userRoutesMap.DAAKIA_VC_MEET.path)}/${encoder(personalMeetingRoomId)}`}
              target="_blank"
              rel="noopener noreferrer"
              className="personal-meeting-modal-link"
            >
              {`${baseUrlGenerator(userRoutesMap.DAAKIA_VC_MEET.path)}/${encoder(personalMeetingRoomId)}`}
            </a>
            <span
              className="personal-meeting-modal-copy"
              onClick={() => {
                const url = `${baseUrlGenerator(userRoutesMap.DAAKIA_VC_MEET.path)}/${encoder(personalMeetingRoomId)}`;
                window.navigator.clipboard.writeText(url);
                modalNotification({
                  type: "success",
                  message: "Link copied to clipboard",
                });
              }}
              title="Copy link"
            >
              <IoMdCopy size={22} />
            </span>
          </div>
        </div> */}
    </Modal>
  );
}
